import time
import datetime
import pyautogui

limit = 10
whileMin = input("主子，您计划摸鱼多久？（可以输入 1 代表1个小时，0.5 代表半个小时)；直接回车就有半小时哦～") or '0.5'
whileMin = float(whileMin)
offset = datetime.timedelta(hours=whileMin)
beginTime = datetime.datetime.now()
leaveTime = (beginTime + offset)
print('当前系统的时间是 :', beginTime)
print('你计划摸鱼到     :', leaveTime.strftime('%Y-%m-%d %H:%M:%S'))
while 1:
    currentTime = datetime.datetime.now()
    if leaveTime < currentTime:
        exit()
    # 添加随机移动，更自然
    import random

    x1 = random.randint(300, 400)
    y1 = random.randint(400, 500)
    pyautogui.moveTo(x1, y1, 1)
    pyautogui.moveTo(x1, y1, 1)

    # 添加延迟，减少CPU占用
    time.sleep(10)